<!--
  * 松布任务单
  * 
  * @Author:    wcc
  * @Date:      2025-06-24
  * @Copyright  zscbdic
-->
<template>
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="松布任务单" class="smart-query-form-item">
        <a-input style="width: 200px" v-model:value="queryForm.taskNumber" placeholder="请输入松布任务单号" allowClear />
      </a-form-item>
      <a-form-item label="松布状态" class="smart-query-form-item smart-margin-left10">
        <a-select v-model:value="queryForm.loosenStatus" style="width: 120px" placeholder="请选择状态" allowClear>
          <a-select-option v-for="item in LOOSEN_TASK_STATUS_ENUM.getOptions()" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item class="smart-query-form-item smart-margin-left10">
        <a-button type="primary" @click="onSearch">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="reset" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
        <a-button type="primary" @click="showFrame" class="smart-margin-left10">
          <template #icon>
            <SearchOutlined />
          </template>
          松布架
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <a-card size="small" :bordered="false" :hoverable="true">
    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="id"
      bordered
      :loading="tableLoading"
      :pagination="false"
      :row-selection="{ selectedRows: selectedRows, onChange: onSelectChange }"
      :scroll="{ x: 2200 }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'loosenStatus'">
          <a-tag :bordered="false" :color="LOOSEN_TASK_STATUS_ENUM.getEnum(Number(record.loosenStatus)).color">
            {{ LOOSEN_TASK_STATUS_ENUM.getEnum(Number(record.loosenStatus)).label }}
          </a-tag>
        </template>
        <template v-if="column.dataIndex === 'loosenTime'">
          <a-tooltip :title="getTimeProgressTooltip(record)" color="blue">
            <a-progress
              :percent="calculateTimeProgress(record)"
              :status="getProgressStatus(record)"
              :format="(percent) => `${percent.toFixed(0)}%`"
            />
          </a-tooltip>
        </template>
      </template>
    </a-table>
    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-card>
  <FrameList ref="frameRef" />
</template>

<script setup>
  import { ref, reactive, onMounted, onUnmounted } from 'vue';
  import { PAGE_SIZE_OPTIONS, PAGE_SIZE } from '/@/constants/common-const';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { loosenPlanDetailsApi } from '/@/api/business/mes/tailor/loosen-plan-details-api.js';
  import { tableMergeCell } from '/@/utils/table-merge-cell-util.js';
  import FrameList from '/@/views/business/mes/tailor/loosen/frame/frame-list.vue';
  import { LOOSEN_TASK_STATUS_ENUM } from '/@/constants/business/mes/tailor/loosen-task-status-const';
  import dayjs from 'dayjs';
  import duration from 'dayjs/plugin/duration';

  //------------------------------------------------表格--------------------------------------------------
  // 表格列定义
  const columns = [
    {
      title: '松布计划单',
      dataIndex: 'fabricLoosenNumber',
      ellipsis: true,
      customCell: (record, index) => tableMergeCell(tableData.value, record, index, 'fabricLoosenNumber'),
    },
    {
      title: '松布状态',
      dataIndex: 'loosenStatus',
      ellipsis: true,
      width: 100,
    },
    {
      title: '松布时长',
      dataIndex: 'loosenTime',
      ellipsis: true,
    },
    {
      title: '卷数',
      dataIndex: 'rolls',
      ellipsis: true,
      width: 70,
      align:'center'
    },
    {
      title: '松布任务单',
      dataIndex: 'taskNumber',
      ellipsis: true,
      width: 150,
    },
    {
      title: '物料编号',
      dataIndex: 'itemNumber',
      ellipsis: true,
      width: 150,
    },
    {
      title: '物料名称',
      dataIndex: 'itemName',
      ellipsis: true,
    },
    {
      title: '颜色',
      dataIndex: 'itemColor',
      ellipsis: true,
    },
    {
      title: '缸号',
      dataIndex: 'lotNo',
      ellipsis: true,
    },
    {
      title: '单位',
      dataIndex: 'itemUnit',
      ellipsis: true,
      width: 70,
    },
    {
      title: '松布架',
      dataIndex: 'frame',
      ellipsis: true,
    },
    {
      title: '计划开始时间',
      dataIndex: 'planedStTime',
      ellipsis: true,
      customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm') : ''),
    },
    {
      title: '计划结束时间',
      dataIndex: 'planedEndTime',
      ellipsis: true,
      customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm') : ''),
    },
    {
      title: '实际开始时间',
      dataIndex: 'realStTime',
      ellipsis: true,
      customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm') : ''),
    },
    {
      title: '实际结束时间',
      dataIndex: 'realEndTime',
      ellipsis: true,
      customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm') : ''),
    },
  ];
  // 表格数据
  const tableLoading = ref(false);
  const tableData = ref([]);
  const total = ref(0);

  // 行数据
  const selectedRows = ref([]);
  // 选择变化的处理函数
  const onSelectChange = (keys, rows) => {
    selectedRows.value = rows;
  };

  //------------------------------------------------表单---------------------------------------------------
  // 查询表单数据
  const queryFormState = {
    pageNum: 1,
    pageSize: 10,
    taskNumber: '',
    loosenStatus: undefined,
  };

  const queryForm = reactive({ ...queryFormState });

  // 查询数据
  async function queryData() {
    tableLoading.value = true;
    try {
      const res = await loosenPlanDetailsApi.queryPage(queryForm);
      tableData.value = res.data.list;
      total.value = res.data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  // 重置查询
  function reset() {
    Object.assign(queryForm, queryFormState);
    queryData();
  }

  // 查询
  function onSearch() {
    queryForm.pageNum = 1;
    queryData();
  }

  //---------------------------------------------松布架-----------------------------------------------
  const frameRef = ref();
  async function showFrame() {
    frameRef.value.show();
  }

  //--------------------------------------松布时长进度条处理-------------------------------------------
  dayjs.extend(duration);

  //定时器
  const refreshTrigger = ref(0);
  let timer = null;
  // 启动定时器函数
  function startProgressTimer() {
    // 每分钟更新一次进度
    timer = setInterval(() => {
      refreshTrigger.value += 1;
    }, 60000);
  }
  // 停止定时器函数
  function stopProgressTimer() {
    if (timer) {
      clearInterval(timer);
      timer = null;
    }
  }

  //计算各种时长
  function calculateLoosenTimes(record) {
    const _ = refreshTrigger.value; //刷新触发器
    // 未开始，返回默认值
    if (!record.realStTime) {
      return {
        isStarted: false,
        isCompleted: false,
        progress: 0,
        elapsedHours: 0,
        elapsedMinutes: 0,
        remainingHours: record.loosenTime,
        remainingMinutes: 0,
        totalHours: record.loosenTime,
      };
    }

    // 判断是否已完成 1：进行中 2：已完成 3：已结束
    const isCompleted = record.loosenStatus === 2 || record.loosenStatus === 3;

    const startTime = dayjs(record.realStTime);
    const currentTime = dayjs();
    const totalDuration = dayjs.duration(record.loosenTime, 'hour'); //松布总时长
    const totalMilliseconds = totalDuration.asMilliseconds();

    // 计算已静置时间
    const elapsedDuration = dayjs.duration(currentTime.diff(startTime));
    const elapsedMilliseconds = elapsedDuration.asMilliseconds();

    // 计算进度百分比
    const progressPercent = Math.floor((elapsedMilliseconds / totalMilliseconds) * 100);
    const progress = isCompleted ? 100 : Math.min(progressPercent, 100);

    // 计算已静置的小时和分钟
    const elapsedHours = Math.floor(elapsedDuration.asHours());
    const elapsedMinutes = Math.floor(elapsedDuration.minutes());

    // 计算剩余时间
    const remainingDuration = dayjs.duration(Math.max(0, totalMilliseconds - elapsedMilliseconds));
    const remainingHours = Math.floor(remainingDuration.asHours());
    const remainingMinutes = Math.floor(remainingDuration.minutes());

    return {
      isStarted: true, //是否开始
      isCompleted, //是否完成
      progress, //进度百分比
      elapsedHours, //已静置的小时
      elapsedMinutes, //已静置的分钟
      remainingHours, //剩余时间的小时
      remainingMinutes, //剩余时间的分钟
      totalHours: record.loosenTime, //总时长
    };
  }

  // 计算松布时长进度
  function calculateTimeProgress(record) {
    return calculateLoosenTimes(record).progress;
  }

  // 获取进度条状态
  function getProgressStatus(record) {
    const times = calculateLoosenTimes(record);

    if (!times.isStarted) return 'normal';
    if (times.isCompleted) return 'success';
    return 'active';
  }

  // 获取进度条提示信息
  function getTimeProgressTooltip(record) {
    const times = calculateLoosenTimes(record);

    return !times.isStarted
      ? '未开始静置'
      : times.isCompleted
      ? `已完成静置 ${times.totalHours} 小时`
      : `已静置 ${times.elapsedHours} 小时 ${times.elapsedMinutes} 分钟，剩余 ${times.remainingHours} 小时 ${times.remainingMinutes} 分钟`;
  }

  // 获取松布状态描述
  function getLoosenStatusLabel(status) {
    console.log(LOOSEN_TASK_STATUS_ENUM.getEnum(status).label);
    return LOOSEN_TASK_STATUS_ENUM.getEnum(status).label;
  }

  onMounted(() => {
    queryData();
    startProgressTimer();
  });

  // 清除定时器
  onUnmounted(() => {
    stopProgressTimer();
  });
</script>
